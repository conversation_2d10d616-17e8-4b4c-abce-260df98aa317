# name: test/sql/streaming_json_reader.test
# description: test streaming_json_reader extension
# group: [json]

# Before we load the extension, this will fail
statement error
SELECT * FROM streaming_json_reader('test.json');
----
Catalog Error: Table Function with name streaming_json_reader does not exist!

# Require statement will ensure the extension is loaded from now on
require streaming_json_reader

require icu

# Create a test JSON file
statement ok
COPY (SELECT '{"name": "test", "value": 42, "active": true}') TO 'test.json' (FORMAT CSV, QUOTE '', HEADER false);

# Confirm the extension works with a simple test
query I
SELECT * FROM streaming_json_reader('test.json');
----
JSON content: JSON object with 3 fields