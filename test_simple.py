#!/usr/bin/env python3
"""
Simple test script - loads DuckDB with the streaming JSON reader extension
and lets you run SQL queries interactively.
"""

import duckdb

# Load DuckDB and extension
conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
conn.execute('LOAD "./build/debug/streaming_json_reader.duckdb_extension"')

print("Extension loaded. Enter SQL queries (Ctrl+C to exit):")
result = conn.execute(query).fetchall()
print(result)

conn.close()
