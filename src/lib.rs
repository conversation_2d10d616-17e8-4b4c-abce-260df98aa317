extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

use duckdb::{
    core::{DataChunkHandle, Inserter, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use std::{
    collections::HashMap,
    error::Error,
    ffi::CString,
    fs::File,
    io::{BufRead, BufReader, Read, Seek, SeekFrom},
    path::PathBuf,
    sync::atomic::{AtomicBool, AtomicUsize, Ordering},
};
use struson::reader::{<PERSON>sonReader, JsonStreamReader};
use thiserror::Error;

#[derive(Error, Debug)]
pub enum JsonReaderError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("JSON parsing error: {0}")]
    Json(#[from] struson::reader::ReaderError),
    #[error("Invalid file path: {0}")]
    InvalidPath(String),
    #[error("Unsupported JSON format: {0}")]
    UnsupportedFormat(String),
    #[error("Schema error: {0}")]
    Schema(String),
}

#[derive(Debug, Clone)]
pub enum JsonFormat {
    /// Newline-delimited JSON (each line is a separate JSON object)
    Ndjson,
    /// Single JSON object with potential array flattening
    SingleObject { flatten_array: Option<String> },
    /// JSON array (top-level array of objects)
    Array,
}

#[derive(Debug)]
pub struct ColumnInfo {
    pub name: String,
    pub logical_type: LogicalTypeId,
    pub json_path: Vec<String>,
}

#[repr(C)]
struct StreamingJsonBindData {
    file_path: PathBuf,
    format: JsonFormat,
    columns: Vec<ColumnInfo>,
    projected_columns: Option<Vec<usize>>,
}

#[repr(C)]
struct StreamingJsonInitData {
    file: Option<File>,
    reader: Option<JsonStreamReader<BufReader<File>>>,
    current_row: AtomicUsize,
    finished: AtomicBool,
    // For NDJSON: current line buffer
    line_buffer: Option<String>,
    // For array flattening: current array position
    array_position: AtomicUsize,
    // For single object: whether we've started reading the main content
    started_main_content: AtomicBool,
}

struct StreamingJsonVTab;

// Helper functions for JSON format detection and schema creation
fn detect_json_format(file_path: &PathBuf) -> Result<JsonFormat, JsonReaderError> {
    let file = File::open(file_path)?;
    let mut buf_reader = BufReader::new(file);
    let mut first_line = String::new();

    // Read the first line to detect format
    buf_reader.read_line(&mut first_line)?;
    let first_line = first_line.trim();

    if first_line.starts_with('[') {
        Ok(JsonFormat::Array)
    } else if first_line.starts_with('{') {
        // Check if this is NDJSON by trying to read a second line
        let mut second_line = String::new();
        if buf_reader.read_line(&mut second_line)? > 0 {
            let second_line = second_line.trim();
            if second_line.starts_with('{') {
                Ok(JsonFormat::Ndjson)
            } else {
                // Single object, check if it has a large array field to flatten
                Ok(JsonFormat::SingleObject { flatten_array: Some("data".to_string()) })
            }
        } else {
            Ok(JsonFormat::SingleObject { flatten_array: None })
        }
    } else {
        Err(JsonReaderError::UnsupportedFormat(
            "File does not appear to contain valid JSON".to_string()
        ))
    }
}

fn create_default_schema(format: &JsonFormat) -> Result<Vec<ColumnInfo>, JsonReaderError> {
    // For now, create a simple default schema
    // In a real implementation, this would analyze the JSON structure
    match format {
        JsonFormat::Ndjson | JsonFormat::Array => {
            Ok(vec![
                ColumnInfo {
                    name: "id".to_string(),
                    logical_type: LogicalTypeId::Varchar,
                    json_path: vec!["id".to_string()],
                },
                ColumnInfo {
                    name: "name".to_string(),
                    logical_type: LogicalTypeId::Varchar,
                    json_path: vec!["name".to_string()],
                },
                ColumnInfo {
                    name: "value".to_string(),
                    logical_type: LogicalTypeId::Varchar,
                    json_path: vec!["value".to_string()],
                },
            ])
        }
        JsonFormat::SingleObject { flatten_array } => {
            let mut columns = vec![
                ColumnInfo {
                    name: "metadata_name".to_string(),
                    logical_type: LogicalTypeId::Varchar,
                    json_path: vec!["name".to_string()],
                },
                ColumnInfo {
                    name: "metadata_date".to_string(),
                    logical_type: LogicalTypeId::Varchar,
                    json_path: vec!["date".to_string()],
                },
            ];

            if let Some(array_field) = flatten_array {
                columns.extend(vec![
                    ColumnInfo {
                        name: "data_id".to_string(),
                        logical_type: LogicalTypeId::Varchar,
                        json_path: vec![array_field.clone(), "id".to_string()],
                    },
                    ColumnInfo {
                        name: "data_value".to_string(),
                        logical_type: LogicalTypeId::Varchar,
                        json_path: vec![array_field.clone(), "value".to_string()],
                    },
                ]);
            }

            Ok(columns)
        }
    }
}

impl VTab for StreamingJsonVTab {
    type InitData = StreamingJsonInitData;
    type BindData = StreamingJsonBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get the file path parameter
        let file_path_param = bind.get_parameter(0).to_string();
        let file_path = PathBuf::from(file_path_param);

        if !file_path.exists() {
            return Err(Box::new(JsonReaderError::InvalidPath(
                format!("File does not exist: {}", file_path.display())
            )));
        }

        // For now, we'll use a simple schema detection approach
        // In a full implementation, this could be more sophisticated
        let format = detect_json_format(&file_path)?;
        let columns = create_default_schema(&format)?;

        // Add result columns to DuckDB
        for column in &columns {
            bind.add_result_column(&column.name, LogicalTypeHandle::from(column.logical_type));
        }

        Ok(StreamingJsonBindData {
            file_path,
            format,
            columns,
            projected_columns: None, // Will be set in init if projection pushdown is enabled
        })
    }

    fn init(_init: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        // For now, we'll defer file opening to the function execution
        // This is because we need to handle the bind data access properly
        Ok(StreamingJsonInitData {
            file: None,
            reader: None,
            current_row: AtomicUsize::new(0),
            finished: AtomicBool::new(false),
            line_buffer: None,
            array_position: AtomicUsize::new(0),
            started_main_content: AtomicBool::new(false),
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let _bind_data = func.get_bind_data();

        if init_data.finished.load(Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        // For now, just return a placeholder result
        // This will be implemented in the next steps
        let vector = output.flat_vector(0);
        let result = CString::new("Streaming JSON Reader - Not Yet Implemented")?;
        vector.insert(0, result);
        output.set_len(1);
        init_data.finished.store(true, Ordering::Relaxed);

        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

const EXTENSION_NAME: &str = env!("CARGO_PKG_NAME");

#[duckdb_entrypoint_c_api()]
pub unsafe fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<StreamingJsonVTab>("streaming_json_reader")
        .expect("Failed to register streaming JSON reader table function");
    Ok(())
}