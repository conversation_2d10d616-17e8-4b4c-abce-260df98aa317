extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

use duckdb::{
    core::{DataChunkHandle, Inserter, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use std::{
    error::Error,
    ffi::CString,
    fs::File,
    io::BufReader,
    path::Path,
    sync::atomic::{AtomicBool, Ordering},
};
use struson::reader::{JsonRead<PERSON>, JsonStreamReader};

#[repr(C)]
struct JsonReaderBindData {
    file_path: String,
}

#[repr(C)]
struct JsonReaderInitData {
    done: AtomicBool,
}

struct JsonReaderVTab;

// Helper function to read JSON file using struson
fn read_json_file(file_path: &str) -> Result<String, Box<dyn std::error::Error>> {
    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(format!("File does not exist: {}", file_path).into());
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    // Try to read the first JSON value and return a summary
    match json_reader.peek()? {
        struson::reader::ValueType::Object => {
            json_reader.begin_object()?;
            let mut field_count = 0;
            while json_reader.has_next()? {
                let _name = json_reader.next_name()?;
                json_reader.skip_value()?;
                field_count += 1;
            }
            json_reader.end_object()?;
            Ok(format!("JSON object with {} fields", field_count))
        }
        struson::reader::ValueType::Array => {
            json_reader.begin_array()?;
            let mut element_count = 0;
            while json_reader.has_next()? {
                json_reader.skip_value()?;
                element_count += 1;
            }
            json_reader.end_array()?;
            Ok(format!("JSON array with {} elements", element_count))
        }
        struson::reader::ValueType::String => {
            let value = json_reader.next_string()?;
            Ok(format!("JSON string: {}", value))
        }
        struson::reader::ValueType::Number => {
            let value = json_reader.next_number_as_str()?;
            Ok(format!("JSON number: {}", value))
        }
        struson::reader::ValueType::Boolean => {
            let value = json_reader.next_bool()?;
            Ok(format!("JSON boolean: {}", value))
        }
        struson::reader::ValueType::Null => {
            json_reader.next_null()?;
            Ok("JSON null".to_string())
        }
    }
}

impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get the file path parameter
        let file_path = bind.get_parameter(0).to_string();

        // Add a simple result column for now
        bind.add_result_column("json_data", LogicalTypeHandle::from(LogicalTypeId::Varchar));

        Ok(JsonReaderBindData {
            file_path,
        })
    }

    fn init(_init: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        Ok(JsonReaderInitData {
            done: AtomicBool::new(false),
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let bind_data = func.get_bind_data();

        if init_data.done.swap(true, Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        // Try to read the JSON file
        let result = match read_json_file(&bind_data.file_path) {
            Ok(content) => format!("JSON content: {}", content),
            Err(e) => format!("Error reading JSON: {}", e),
        };

        let vector = output.flat_vector(0);
        let result_cstring = CString::new(result)?;
        vector.insert(0, result_cstring);
        output.set_len(1);

        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

const EXTENSION_NAME: &str = env!("CARGO_PKG_NAME");

#[duckdb_entrypoint_c_api()]
pub unsafe fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .expect("Failed to register streaming JSON reader table function");
    Ok(())
}