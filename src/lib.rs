extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

use duckdb::{
    core::{DataChunkHandle, Inserter, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use std::{
    error::Error,
    ffi::CString,
    fs::File,
    io::{BufRead, BufReader},
    path::Path,
    sync::atomic::{AtomicBool, AtomicUsize, Ordering},
};
use struson::reader::{JsonReader, JsonStreamReader};

#[repr(C)]
struct JsonReaderBindData {
    file_path: String,
    json_path: Option<String>, // Path to the array to flatten (e.g., "users" or "users.projects")
    columns: Vec<String>, // Column names to extract from each array element
}

#[repr(C)]
struct JsonReaderInitData {
    current_element: AtomicUsize,
    finished: AtomicBool,
}

struct JsonReaderVTab;

// Helper function to read and flatten JSON arrays using struson
fn read_and_flatten_json(
    file_path: &str,
    json_path: &Option<String>,
    columns: &[String],
    init_data: &JsonReaderInitData
) -> Result<Vec<Vec<String>>, Box<dyn std::error::Error>> {
    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(format!("File does not exist: {}", file_path).into());
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    // Navigate to the specified JSON path if provided
    if let Some(path) = json_path {
        navigate_to_path(&mut json_reader, path)?;
    }

    // Read array elements and flatten them
    let current_element = init_data.current_element.load(Ordering::Relaxed);
    let batch_size = 10; // Process 10 elements at a time

    match json_reader.peek()? {
        struson::reader::ValueType::Array => {
            json_reader.begin_array()?;

            // Skip to current position
            for _ in 0..current_element {
                if json_reader.has_next()? {
                    json_reader.skip_value()?;
                } else {
                    return Ok(vec![]); // No more elements
                }
            }

            // Read batch of elements
            let mut result_columns: Vec<Vec<String>> = vec![Vec::new(); columns.len()];
            let mut elements_read = 0;

            while json_reader.has_next()? && elements_read < batch_size {
                if let struson::reader::ValueType::Object = json_reader.peek()? {
                    json_reader.begin_object()?;
                    let mut row_data = vec!["".to_string(); columns.len()];

                    while json_reader.has_next()? {
                        let field_name = json_reader.next_name()?;
                        if let Some(col_idx) = columns.iter().position(|c| c == &field_name) {
                            // Extract the value for this column
                            let value = match json_reader.peek()? {
                                struson::reader::ValueType::String => json_reader.next_string()?,
                                struson::reader::ValueType::Number => json_reader.next_number_as_str()?.to_string(),
                                struson::reader::ValueType::Boolean => json_reader.next_bool()?.to_string(),
                                struson::reader::ValueType::Null => {
                                    json_reader.next_null()?;
                                    "null".to_string()
                                }
                                _ => {
                                    json_reader.skip_value()?;
                                    "".to_string()
                                }
                            };
                            row_data[col_idx] = value;
                        } else {
                            json_reader.skip_value()?;
                        }
                    }
                    json_reader.end_object()?;

                    // Add row data to result columns
                    for (col_idx, value) in row_data.into_iter().enumerate() {
                        result_columns[col_idx].push(value);
                    }
                    elements_read += 1;
                } else {
                    json_reader.skip_value()?;
                    elements_read += 1;
                }
            }

            json_reader.end_array()?;

            // Update current position
            init_data.current_element.store(current_element + elements_read, Ordering::Relaxed);

            // Mark as finished if no more elements
            if elements_read == 0 || !json_reader.has_next()? {
                init_data.finished.store(true, Ordering::Relaxed);
            }

            Ok(result_columns)
        }
        _ => {
            Err("Expected JSON array at the specified path".into())
        }
    }
}

// Helper function to navigate to a specific JSON path
fn navigate_to_path(json_reader: &mut JsonStreamReader<BufReader<File>>, path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let path_parts: Vec<&str> = path.split('.').collect();
    let last_part_index = path_parts.len() - 1;

    // Start with the root object
    if let struson::reader::ValueType::Object = json_reader.peek()? {
        json_reader.begin_object()?;

        for (index, part) in path_parts.iter().enumerate() {
            let mut found = false;
            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?;
                if field_name == *part {
                    found = true;
                    break;
                } else {
                    json_reader.skip_value()?;
                }
            }

            if !found {
                return Err(format!("Path component '{}' not found", part).into());
            }

            // If this is not the last part, expect an object
            if index != last_part_index {
                if let struson::reader::ValueType::Object = json_reader.peek()? {
                    json_reader.begin_object()?;
                } else {
                    return Err(format!("Expected object at path component '{}'", part).into());
                }
            }
        }
    } else {
        return Err("Expected JSON object at root".into());
    }

    Ok(())
}

impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get the file path parameter
        let file_path = bind.get_parameter(0).to_string();

        // Get optional JSON path parameter (e.g., "users" to flatten the users array)
        let json_path = if bind.get_parameter_count() > 1 {
            Some(bind.get_parameter(1).to_string())
        } else {
            None
        };

        // For now, use default columns - in a real implementation this could be configurable
        let columns = vec!["id".to_string(), "name".to_string(), "value".to_string()];

        // Add result columns to DuckDB
        for column in &columns {
            bind.add_result_column(column, LogicalTypeHandle::from(LogicalTypeId::Varchar));
        }

        Ok(JsonReaderBindData {
            file_path,
            json_path,
            columns,
        })
    }

    fn init(_init: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        Ok(JsonReaderInitData {
            current_element: AtomicUsize::new(0),
            finished: AtomicBool::new(false),
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let bind_data = func.get_bind_data();

        if init_data.finished.load(Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        // Try to read and flatten the JSON array
        match read_and_flatten_json(&bind_data.file_path, &bind_data.json_path, &bind_data.columns, init_data) {
            Ok(rows) => {
                if rows.is_empty() {
                    init_data.finished.store(true, Ordering::Relaxed);
                    output.set_len(0);
                } else {
                    // Fill the output vectors with the row data
                    for (col_idx, column_data) in rows.iter().enumerate() {
                        let vector = output.flat_vector(col_idx);
                        for (row_idx, value) in column_data.iter().enumerate() {
                            let cstring = CString::new(value.as_str())?;
                            vector.insert(row_idx, cstring);
                        }
                    }
                    output.set_len(rows[0].len());
                }
            }
            Err(e) => {
                // Return error as a single row
                let vector = output.flat_vector(0);
                let error_msg = CString::new(format!("Error: {}", e))?;
                vector.insert(0, error_msg);
                output.set_len(1);
                init_data.finished.store(true, Ordering::Relaxed);
            }
        }

        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![
            LogicalTypeHandle::from(LogicalTypeId::Varchar), // file_path
        ])
    }
}

const EXTENSION_NAME: &str = env!("CARGO_PKG_NAME");

#[duckdb_entrypoint_c_api()]
pub unsafe fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .expect("Failed to register streaming JSON reader table function");
    Ok(())
}